import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface SpecsLoadingSkeletonProps {
  viewMode?: 'card' | 'list';
  count?: number;
}

const SpecsLoadingSkeleton: React.FC<SpecsLoadingSkeletonProps> = ({ 
  viewMode = 'card', 
  count = 6 
}) => {
  return (
    <div className={viewMode === 'card' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
      {[...Array(count)].map((_, i) => (
        <div key={i}>
          <Card>
            <CardHeader className="pb-2">
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-8 w-24" />
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  );
};

export default SpecsLoadingSkeleton;
