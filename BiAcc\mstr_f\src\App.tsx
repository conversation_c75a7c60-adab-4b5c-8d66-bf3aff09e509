// import { Toaster } from "@/components/ui/toaster";
// import { Toaster as Sonner } from "@/components/ui/sonner";
// import { TooltipProvider } from "@/components/ui/tooltip";
// import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
// import { BrowserRouter, Routes, Route, Navigate, useNavigate, useLocation } from "react-router-dom";
// import { useEffect, useState } from "react";
// import Layout from "./components/layout/Layout";
// import Index from "./pages/Index";
// import LoginPage from "./pages/LoginPage";
// import ProjectsPage from "./pages/ProjectsPage";
// import ProjectDetail from "./pages/ProjectDetail";
// import ReportsPage from "./pages/reports/ReportsPage";
// import ReportDetail from "./pages/reports/ReportDetail";
// import FFSQLReportsPage from "./pages/ffsql-reports/FFSQLReportsPage";
// import CubesPage from "./pages/cubes/CubesPage";
// import CubeDetail from "./pages/cubes/CubeDetail";
// import DashboardsPage from "./pages/dashboards/DashboardsPage";
// import DashboardDetail from "./pages/dashboards/DashboardDetail";
// import NotFound from "./pages/NotFound";
// import { apiService } from "./services/api";
// import { ViewModeProvider } from "./lib/ViewModeContext";
// import DaxConversionsPage from "./pages/dax/DaxConversionsPage";

// const queryClient = new QueryClient({
//   defaultOptions: {
//     queries: {
//       refetchOnWindowFocus: false,
//       retry: 1,
//     },
//   },
// });

// // Authentication check component
// const AuthCheck = ({ children }: { children: React.ReactNode }) => {
//   const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
//   const navigate = useNavigate();
//   const location = useLocation();

//   // Don't redirect if already on login page
//   if (location.pathname === '/') {
//     return <>{children}</>;
//   }

//   useEffect(() => {
//     // Check if user is authenticated
//     const checkAuth = () => {
//       const isAuth = localStorage.getItem('isAuthenticated');
//       if (isAuth === 'true') {
//         setIsAuthenticated(true);
//       } else {
//         setIsAuthenticated(false);
//         navigate('/login', { state: { from: location.pathname } });
//       }
//     };

//     checkAuth();
//   }, [navigate, location]);

//   // Show nothing while checking
//   if (isAuthenticated === null) {
//     return null;
//   }

//   // If authenticated, render the children
//   return isAuthenticated ? <>{children}</> : null;
// };

// // Protected route component that checks for environments
// const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
//   const [isConnected, setIsConnected] = useState<boolean | null>(null);
//   const [isLoading, setIsLoading] = useState(true);
//   const navigate = useNavigate();
//   const location = useLocation();

//   useEffect(() => {
//     const checkEnvironments = async () => {
//       setIsLoading(true);
//       try {
//         const environments = await apiService.getEnvironments();
        
//         // Check if there are any configured environments
//         if (environments && environments.environments && environments.environments.length > 0) {
//           setIsConnected(true);
          
//           // Also check if there's an active environment
//           if (!environments.active_environment && environments.environments.length > 0) {
//             // If no active environment but we have environments, redirect to home to select one
//             navigate('/', { state: { from: location.pathname } });
//             return;
//           }
//         } else {
//           setIsConnected(false);
//           // Redirect to home page, storing the intended destination
//           navigate('/', { state: { from: location.pathname } });
//         }
//       } catch (error) {
//         console.error('Failed to check environments:', error);
//         setIsConnected(false);
//         navigate('/');
//       } finally {
//         setIsLoading(false);
//       }
//     };

//     checkEnvironments();
//   }, [navigate, location]);

//   // Show nothing while checking
//   if (isLoading || isConnected === null) {
//     return null;
//   }

//   // If connected, render the children
//   return isConnected ? <>{children}</> : null;
// };

// const App = () => (
//   <QueryClientProvider client={queryClient}>
//     <TooltipProvider>
//       <ViewModeProvider>
//         <Toaster />
//         <Sonner />
//         <BrowserRouter>
//           <Routes>
//             {/* Login route outside of Layout */}
//             <Route path="/login" element={<LoginPage />} />
            
//             {/* All other routes wrapped in Layout */}
//             <Route path="/" element={
//               <Layout>
//                 <AuthCheck>
//                   <Index />
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/projects" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <ProjectsPage />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/project/:id" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <ProjectDetail />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/reports" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <ReportsPage />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/reports/:id" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <ReportDetail />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/ffsql-reports" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <FFSQLReportsPage />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/cubes" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <CubesPage />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/cubes/:id" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <CubeDetail />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/dashboards" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <DashboardsPage />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/dashboards/:id" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <DashboardDetail />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="/dax" element={
//               <Layout>
//                 <AuthCheck>
//                   <ProtectedRoute>
//                     <DaxConversionsPage />
//                   </ProtectedRoute>
//                 </AuthCheck>
//               </Layout>
//             } />
//             <Route path="*" element={<Layout><Navigate to="/" replace /></Layout>} />
//           </Routes>
//         </BrowserRouter>
//       </ViewModeProvider>
//     </TooltipProvider>
//   </QueryClientProvider>
// );

// export default App;
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useNavigate, useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import Layout from "./components/layout/Layout";
import Index from "./pages/Index";
import ProjectsPage from "./pages/ProjectsPage";
import ProjectDetail from "./pages/ProjectDetail";
import ReportsPage from "./pages/reports/ReportsPage";
import ReportDetail from "./pages/reports/ReportDetail";
import FFSQLReportsPage from "./pages/ffsql-reports/FFSQLReportsPage";
import CubesPage from "./pages/cubes/CubesPage";
import CubeDetail from "./pages/cubes/CubeDetail";
import DashboardsPage from "./pages/dashboards/DashboardsPage";
import DashboardDetail from "./pages/dashboards/DashboardDetail";
import NotFound from "./pages/NotFound";
import { apiService } from "./services/api";
import { ViewModeProvider } from "./lib/ViewModeContext";
import DaxConversionsPage from "./pages/dax/DaxConversionsPage";
import {
  SpecsPage,
  SpecsDetail,
  SpecCubesPage,
  SpecReportsPage,
  SpecCubeDetail,
  SpecReportDetail
} from "./pages/Specs";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

// Protected route component that checks for environments
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const checkEnvironments = async () => {
      setIsLoading(true);
      try {
        const environments = await apiService.getEnvironments();
        
        // Check if there are any configured environments
        if (environments && environments.environments && environments.environments.length > 0) {
          setIsConnected(true);
          
          // Also check if there's an active environment
          if (!environments.active_environment && environments.environments.length > 0) {
            // If no active environment but we have environments, redirect to home to select one
            navigate('/', { state: { from: location.pathname } });
            return;
          }
        } else {
          setIsConnected(false);
          // Redirect to home page, storing the intended destination
          navigate('/', { state: { from: location.pathname } });
        }
      } catch (error) {
        console.error('Failed to check environments:', error);
        setIsConnected(false);
        navigate('/');
      } finally {
        setIsLoading(false);
      }
    };

    checkEnvironments();
  }, [navigate, location]);

  // Show nothing while checking
  if (isLoading || isConnected === null) {
    return null;
  }

  // If connected, render the children
  return isConnected ? <>{children}</> : null;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <ViewModeProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Redirect login to home */}
            <Route path="/login" element={<Navigate to="/" replace />} />
            
            {/* All routes wrapped in Layout - no authentication required */}
            <Route path="/" element={
              <Layout>
                <Index />
              </Layout>
            } />
            <Route path="/projects" element={
              <Layout>
                <ProtectedRoute>
                  <ProjectsPage />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/project/:id" element={
              <Layout>
                <ProtectedRoute>
                  <ProjectDetail />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/reports" element={
              <Layout>
                <ProtectedRoute>
                  <ReportsPage />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/reports/:id" element={
              <Layout>
                <ProtectedRoute>
                  <ReportDetail />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/ffsql-reports" element={
              <Layout>
                <ProtectedRoute>
                  <FFSQLReportsPage />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/cubes" element={
              <Layout>
                <ProtectedRoute>
                  <CubesPage />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/cubes/:id" element={
              <Layout>
                <ProtectedRoute>
                  <CubeDetail />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/dashboards" element={
              <Layout>
                <ProtectedRoute>
                  <DashboardsPage />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/dashboards/:id" element={
              <Layout>
                <ProtectedRoute>
                  <DashboardDetail />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/dax" element={
              <Layout>
                <ProtectedRoute>
                  <DaxConversionsPage />
                </ProtectedRoute>
              </Layout>
            } />
            <Route path="/specs" element={
              <Layout>
                <SpecsPage />
              </Layout>
            } />
            <Route path="/specs/:id" element={
              <Layout>
                <SpecsDetail />
              </Layout>
            } />
            <Route path="/specs/:id/cubes" element={
              <Layout>
                <SpecCubesPage />
              </Layout>
            } />
            <Route path="/specs/:id/cubes/:cubeId" element={
              <Layout>
                <SpecCubeDetail />
              </Layout>
            } />
            <Route path="/specs/:id/reports" element={
              <Layout>
                <SpecReportsPage />
              </Layout>
            } />
            <Route path="/specs/:id/reports/:reportId" element={
              <Layout>
                <SpecReportDetail />
              </Layout>
            } />
            <Route path="*" element={<Layout><Navigate to="/" replace /></Layout>} />
          </Routes>
        </BrowserRouter>
      </ViewModeProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;