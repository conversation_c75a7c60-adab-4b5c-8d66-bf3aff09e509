import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BreadcrumbItem {
  label: string;
  path?: string;
  isActive?: boolean;
}

interface SpecsBreadcrumbProps {
  items: BreadcrumbItem[];
}

const SpecsBreadcrumb: React.FC<SpecsBreadcrumbProps> = ({ items }) => {
  return (
    <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {item.path && !item.isActive ? (
            <Button variant="link" className="p-0 h-auto" asChild>
              <Link to={item.path}>{item.label}</Link>
            </Button>
          ) : (
            <span className={item.isActive ? "font-medium text-foreground truncate max-w-xs" : ""}>
              {item.label}
            </span>
          )}
          {index < items.length - 1 && <ChevronRight className="h-4 w-4" />}
        </React.Fragment>
      ))}
    </div>
  );
};

export default SpecsBreadcrumb;
