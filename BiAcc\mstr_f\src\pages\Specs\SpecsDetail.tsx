import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { ChevronRight, Database, FileText, ArrowLeft } from 'lucide-react';
import { Project } from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const SpecsDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  useEffect(() => {
    // Try to get project from localStorage
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      const project = JSON.parse(storedProject);
      if (project.id === id) {
        setSelectedProject(project);
        return;
      }
    }
    
    // If no matching project in localStorage, redirect to specs page
    navigate('/specs');
  }, [id, navigate]);

  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-muted-foreground mb-4">Loading project details...</p>
            <Button onClick={() => navigate('/specs')}>
              Back to Specs
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const resourceCards = [
    {
      title: 'Cubes',
      description: 'Browse and analyze cubes in this project',
      icon: Database,
      path: `/specs/${id}/cubes`,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Reports',
      description: 'Explore reports and their structures',
      icon: FileText,
      path: `/specs/${id}/reports`,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
  ];

  return (
    <div className="container px-4 mx-auto animate-fade-in">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 text-sm mb-6">
        <Button variant="link" className="p-0 h-auto text-orange-500 hover:text-orange-600" asChild>
          <Link to="/">Home</Link>
        </Button>
        <span className="text-muted-foreground">›</span>
        <Button variant="link" className="p-0 h-auto text-orange-500 hover:text-orange-600" asChild>
          <Link to="/specs">Projects</Link>
        </Button>
        <span className="text-muted-foreground">›</span>
        <span className="font-medium text-foreground truncate max-w-xs">
          {selectedProject.name}
        </span>
      </div>

      {/* Back Button */}
      <div className="mb-4">
        <Button variant="ghost" onClick={() => navigate('/specs')} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Specs
        </Button>
      </div>
      
      {/* Project Header */}
      <div className="flex flex-col mb-8">
        <h1 className="text-2xl font-bold mb-1">{selectedProject.name}</h1>
        <p className="text-muted-foreground mb-6">
          Browse and analyze data resources in this project
        </p>
      </div>

      {/* Resource Cards */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Project Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {resourceCards.map((resource) => (
            <Card 
              key={resource.title} 
              className="hover:shadow-md transition-all duration-300 group cursor-pointer"
              onClick={() => navigate(resource.path)}
            >
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${resource.bgColor}`}>
                    <resource.icon className={`h-5 w-5 ${resource.color}`} />
                  </div>
                  <span>{resource.title}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="mb-4">
                  {resource.description}
                </CardDescription>
                <Button
                  variant="outline"
                  className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors"
                >
                  Browse {resource.title}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Project Information */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Project Information</h2>
        <Card>
          <CardHeader>
            <CardTitle>Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Project ID</p>
                <p className="text-sm">{selectedProject.id}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Access Status</p>
                <p className="text-sm">
                  {selectedProject.has_access ? (
                    <span className="text-green-600">Accessible</span>
                  ) : (
                    <span className="text-red-600">Restricted</span>
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SpecsDetail;
