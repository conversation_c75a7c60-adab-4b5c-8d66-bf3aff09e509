import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ChevronRight, ArrowLeft, FileText, Eye, ExternalLink } from 'lucide-react';
import { apiService, Project } from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const SpecReportDetail = () => {
  const { id, reportId } = useParams<{ id: string; reportId: string }>();
  const navigate = useNavigate();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  useEffect(() => {
    // Try to get project from localStorage
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      const project = JSON.parse(storedProject);
      if (project.id === id) {
        setSelectedProject(project);
        return;
      }
    }
    
    // If no matching project in localStorage, redirect to specs page
    navigate('/specs');
  }, [id, navigate]);

  const { data: reportDetail, isLoading, error } = useQuery({
    queryKey: ['report-detail', reportId, id],
    queryFn: () => selectedProject && reportId ? apiService.getReportDetail(reportId, selectedProject.id) : Promise.resolve(null),
    enabled: !!selectedProject && !!reportId,
  });

  const handleViewReport = () => {
    if (!reportDetail || !selectedProject) return;
    
    // Navigate to the existing report detail page
    navigate(`/reports/${reportId}?project_id=${selectedProject.id}`);
  };

  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-muted-foreground mb-4">Loading project details...</p>
            <Button onClick={() => navigate('/specs')}>
              Back to Specs
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="bg-red-50 text-red-800 px-4 py-3 rounded-md mb-6 border border-red-200">
          <p className="font-medium">Failed to load report details</p>
          <p className="text-sm mt-1">Please check your connection and try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container px-4 mx-auto animate-fade-in">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" asChild>
          <Link to="/">Home</Link>
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" asChild>
          <Link to="/specs">Specs</Link>
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" asChild>
          <Link to={`/specs/${id}`}>{selectedProject.name}</Link>
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" asChild>
          <Link to={`/specs/${id}/reports`}>Reports</Link>
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          {reportDetail?.name || 'Report Details'}
        </span>
      </div>

      {/* Back Button */}
      <div className="mb-4">
        <Button variant="ghost" onClick={() => navigate(`/specs/${id}/reports`)} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Reports
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-6">
          <div>
            <Skeleton className="h-8 w-1/3 mb-2" />
            <Skeleton className="h-4 w-2/3" />
          </div>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-1/4" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </CardContent>
          </Card>
        </div>
      ) : reportDetail ? (
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col">
            <div className="flex items-center gap-3 mb-2">
              <FileText className="h-6 w-6 text-green-600" />
              <h1 className="text-2xl font-bold">{reportDetail.name}</h1>
            </div>
            <p className="text-muted-foreground mb-4">
              View report details and analyze structure
            </p>
            <div className="flex gap-2">
              <Badge variant="secondary">{reportDetail.type}</Badge>
              <Button onClick={handleViewReport} className="gap-2">
                <Eye className="h-4 w-4" />
                View Report
              </Button>
            </div>
          </div>

          {/* Report Details */}
          <Tabs defaultValue="overview" className="w-full">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="structure">Structure</TabsTrigger>
              <TabsTrigger value="properties">Properties</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Report Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Report ID</p>
                      <p className="text-sm">{reportDetail.id}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Type</p>
                      <p className="text-sm">{reportDetail.type}</p>
                    </div>
                    {reportDetail.properties?.description && (
                      <div className="md:col-span-2">
                        <p className="text-sm font-medium text-muted-foreground">Description</p>
                        <p className="text-sm">{reportDetail.properties.description}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {reportDetail.properties && (
                <Card>
                  <CardHeader>
                    <CardTitle>Metadata</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {reportDetail.properties.date_created && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Created</p>
                          <p className="text-sm">{new Date(reportDetail.properties.date_created).toLocaleDateString()}</p>
                        </div>
                      )}
                      {reportDetail.properties.date_modified && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Modified</p>
                          <p className="text-sm">{new Date(reportDetail.properties.date_modified).toLocaleDateString()}</p>
                        </div>
                      )}
                      {reportDetail.properties.owner && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Owner</p>
                          <p className="text-sm">{reportDetail.properties.owner}</p>
                        </div>
                      )}
                      {reportDetail.properties.version && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Version</p>
                          <p className="text-sm">{reportDetail.properties.version}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="structure" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Report Structure</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {reportDetail.attributes && reportDetail.attributes.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Attributes ({reportDetail.attributes.length})</h4>
                        <div className="space-y-1">
                          {reportDetail.attributes.map((attribute) => (
                            <div key={attribute.id} className="flex items-center justify-between p-2 border rounded text-sm">
                              <span>{attribute.name}</span>
                              <Badge variant="outline" className="text-xs">{attribute.id}</Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {reportDetail.metrics && reportDetail.metrics.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Metrics ({reportDetail.metrics.length})</h4>
                        <div className="space-y-1">
                          {reportDetail.metrics.map((metric) => (
                            <div key={metric.id} className="flex items-center justify-between p-2 border rounded text-sm">
                              <span>{metric.name}</span>
                              <Badge variant="outline" className="text-xs">{metric.id}</Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {(!reportDetail.attributes || reportDetail.attributes.length === 0) && 
                     (!reportDetail.metrics || reportDetail.metrics.length === 0) && (
                      <p className="text-muted-foreground">No structure information available</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="properties" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Advanced Properties</CardTitle>
                </CardHeader>
                <CardContent>
                  {reportDetail.properties ? (
                    <div className="space-y-4">
                      {Object.entries(reportDetail.properties).map(([key, value]) => (
                        <div key={key} className="grid grid-cols-3 gap-4 py-2 border-b">
                          <div className="font-medium text-sm capitalize">
                            {key.replace(/_/g, ' ')}
                          </div>
                          <div className="col-span-2 text-sm text-muted-foreground">
                            {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No additional properties available</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <FileText className="h-8 w-8 text-muted-foreground" />
          </div>
          <h2 className="text-xl font-semibold mb-2">Report Not Found</h2>
          <p className="text-muted-foreground max-w-md mx-auto mb-6">
            The requested report could not be found or you don't have access to it.
          </p>
          <Button onClick={() => navigate(`/specs/${id}/reports`)}>
            Back to Reports
          </Button>
        </div>
      )}
    </div>
  );
};

export default SpecReportDetail;
