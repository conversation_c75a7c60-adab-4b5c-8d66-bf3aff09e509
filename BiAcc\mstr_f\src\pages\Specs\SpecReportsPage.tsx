import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ChevronRight, Search, Grid, List, FileText, ArrowLeft, File } from 'lucide-react';
import { apiService, Project, Report } from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useViewMode } from '@/lib/ViewModeContext';

const SpecReportsPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const { viewMode, setViewMode } = useViewMode();

  useEffect(() => {
    // Try to get project from localStorage
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      const project = JSON.parse(storedProject);
      if (project.id === id) {
        setSelectedProject(project);
        return;
      }
    }
    
    // If no matching project in localStorage, redirect to specs page
    navigate('/specs');
  }, [id, navigate]);

  const { data: reports, isLoading, error } = useQuery({
    queryKey: ['reports', id],
    queryFn: () => selectedProject ? apiService.getReports(selectedProject.id) : Promise.resolve([]),
    enabled: !!selectedProject,
  });

  const filteredReports = searchTerm 
    ? reports?.filter(report => 
        report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.type?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : reports;

  const handleReportClick = (report: Report) => {
    navigate(`/specs/${id}/reports/${report.id}`);
  };

  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-muted-foreground mb-4">Loading project details...</p>
            <Button onClick={() => navigate('/specs')}>
              Back to Specs
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="bg-red-50 text-red-800 px-4 py-3 rounded-md mb-6 border border-red-200">
          <p className="font-medium">Failed to load reports</p>
          <p className="text-sm mt-1">Please check your connection and try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container px-4 mx-auto animate-fade-in">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 text-sm mb-6">
        <Button variant="link" className="p-0 h-auto text-orange-500 hover:text-orange-600" asChild>
          <Link to="/">Home</Link>
        </Button>
        <span className="text-muted-foreground">›</span>
        <Button variant="link" className="p-0 h-auto text-orange-500 hover:text-orange-600" asChild>
          <Link to="/specs">Projects</Link>
        </Button>
        <span className="text-muted-foreground">›</span>
        <Button variant="link" className="p-0 h-auto text-orange-500 hover:text-orange-600" asChild>
          <Link to={`/specs/${id}`}>{selectedProject.name}</Link>
        </Button>
        <span className="text-muted-foreground">›</span>
        <span className="font-medium text-foreground">Reports</span>
      </div>

      {/* Back Button */}
      <div className="mb-4">
        <Button variant="ghost" onClick={() => navigate(`/specs/${id}`)} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Project
        </Button>
      </div>
      
      {/* Header */}
      <div className="flex flex-col mb-8">
        <h1 className="text-2xl font-bold mb-1">Reports</h1>
        <p className="text-muted-foreground mb-6">
          Browse and analyze reports in {selectedProject.name}
        </p>
        
        {/* Search and View Toggle */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="relative max-w-md w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search reports..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              disabled={isLoading}
            />
          </div>
          <div className="flex items-center gap-4">
            <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'card' | 'list')}>
              <ToggleGroupItem value="card" aria-label="Card view">
                <Grid className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="list" aria-label="List view">
                <List className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className={viewMode === 'card' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
          {[...Array(6)].map((_, i) => (
            <div key={i}>
              <Card>
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-8 w-24" />
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      ) : filteredReports && filteredReports.length > 0 ? (
        /* Reports List */
        <div className={viewMode === 'card' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
          {filteredReports.map((report, index) => (
            <Card 
              key={report.id} 
              className={`group transition-all duration-300 animate-slide-up delay-${index * 50} cursor-pointer hover:shadow-md hover:-translate-y-1`}
              onClick={() => handleReportClick(report)}
            >
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-green-600" />
                  <span className="truncate">{report.name}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  {report.type && (
                    <Badge variant="secondary" className="text-xs">
                      {report.type}
                    </Badge>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    View →
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        /* Empty State */
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <File className="h-8 w-8 text-muted-foreground" />
          </div>
          <h2 className="text-xl font-semibold mb-2">No Reports Found</h2>
          <p className="text-muted-foreground max-w-md mx-auto mb-6">
            {searchTerm 
              ? `No reports matching "${searchTerm}" were found in this project.` 
              : 'There are no reports available in this project.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default SpecReportsPage;
