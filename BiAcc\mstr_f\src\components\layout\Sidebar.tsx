import React, { useEffect, useState } from 'react';
import { Link, useLocation, useParams, useNavigate } from 'react-router-dom';
import {
  ChevronLeft,
  ChevronRight,
  Home,
  Database,
  BarChart3,
  LayoutDashboard,
  GitBranch,
  HelpCircle,
  ArrowLeft,
  Code,
  ChevronDown,
  Lock,
  Folder,
  Settings,
  FileText
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Project, apiService } from '@/services/api';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useQuery } from '@tanstack/react-query';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent } from "@/components/ui/dialog";
import EnvironmentManager from '@/components/EnvironmentManager';

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, toggleSidebar }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { id: projectId } = useParams();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isInProject, setIsInProject] = useState(false);
  const [hasActiveEnvironment, setHasActiveEnvironment] = useState<boolean>(false);
  
  const { data: projects, isLoading } = useQuery({
    queryKey: ['projects'],
    queryFn: () => apiService.getProjects(),
  });

  useEffect(() => {
    // Check if we're in a project page
    if (location.pathname.startsWith('/project/')) {
      setIsInProject(true);
      // Get project from localStorage
      const storedProject = localStorage.getItem('selectedProject');
      if (storedProject) {
        setSelectedProject(JSON.parse(storedProject));
      }
    } else {
      setIsInProject(false);
      // Get project from localStorage for other pages
      const storedProject = localStorage.getItem('selectedProject');
      if (storedProject) {
        setSelectedProject(JSON.parse(storedProject));
      }
    }
  }, [location.pathname]);

  // Check for active environment
  useEffect(() => {
    const checkActiveEnvironment = async () => {
      try {
        const environments = await apiService.getEnvironments();
        setHasActiveEnvironment(!!environments.active_environment);
      } catch (error) {
        console.error('Failed to check active environment:', error);
        setHasActiveEnvironment(false);
      }
    };

    checkActiveEnvironment();
  }, [location.pathname]);

  const handleProjectSelect = (project: Project) => {
    if (!project.has_access) return;
    
    // Store selected project in localStorage
    localStorage.setItem('selectedProject', JSON.stringify(project));
    setSelectedProject(project);
    
    // Navigate to project page
    navigate(`/project/${project.id}`);
  };
  
  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };
  
  const sidebarWidth = isOpen ? 'w-64' : 'w-24';

  // Navigation groups for consistent sidebar structure
  const menuGroups = [
    {
      title: "Navigation",
      items: [
        {
          path: '/',
          icon: Home,
          label: 'Home'
        },
        { 
          path: '/projects', 
          icon: GitBranch, 
          label: 'Projects',
          disabled: !hasActiveEnvironment,
          customComponent: (
            <DropdownMenu>
              <DropdownMenuTrigger asChild disabled={!hasActiveEnvironment}>
                <div className={cn(
                  "flex items-center w-full cursor-pointer",
                  "hover:bg-accent/50 transition-colors duration-200",
                  isActive('/projects') && "bg-brand-50 text-brand-600",
                  !hasActiveEnvironment && "opacity-50 cursor-not-allowed pointer-events-none"
                )}>
                  <GitBranch className="h-5 w-5" />
                  {isOpen && <span className="ml-3">Projects</span>}
                  {isOpen && <ChevronDown className="ml-auto h-4 w-4 opacity-50" />}
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="start" 
                className="w-56 p-2"
                sideOffset={5}
              >
                <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
                  Select a Project
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="my-2" />
                {isLoading ? (
                  <div className="space-y-2 p-2">
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : projects?.length === 0 ? (
                  <DropdownMenuItem disabled className="text-muted-foreground">
                    No projects available
                  </DropdownMenuItem>
                ) : (
                  <>
                    {projects?.filter(p => p.has_access).map((project) => (
                      <DropdownMenuItem
                        key={project.id}
                        onClick={() => handleProjectSelect(project)}
                        className="flex items-center justify-between py-2 px-3 rounded-md hover:bg-accent/50"
                      >
                        <div className="flex items-center">
                          <Folder className="h-4 w-4 mr-2" />
                          <span className="font-medium">{project.name}</span>
                        </div>
                        {selectedProject?.id === project.id && (
                          <span className="text-xs text-brand-500">Current</span>
                        )}
                      </DropdownMenuItem>
                    ))}
                    {projects?.some(p => !p.has_access) && (
                      <>
                        <DropdownMenuSeparator className="my-2" />
                        <DropdownMenuLabel className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                          <Lock className="h-3 w-3" />
                          No Access
                        </DropdownMenuLabel>
                        
                        {projects?.filter(p => !p.has_access).map((project) => (
                          <DropdownMenuItem
                            key={project.id}
                            disabled
                            className="flex items-center justify-between py-2 px-3 rounded-md opacity-50"
                          >
                            <div className="flex items-center">
                              
                              <span className="font-medium">{project.name}</span>
                            </div>
                          </DropdownMenuItem>
                        ))}
                      </>
                    )}
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )
        }
      ]
    },
    {
      title: "Content",
      items: [
        {
          path: '/cubes',
          icon: Database,
          label: 'Cubes',
          disabled: !selectedProject
        },
        {
          path: '/reports',
          icon: BarChart3,
          label: 'Reports',
          disabled: !selectedProject
        },
        {
          path: '/dashboards',
          icon: LayoutDashboard,
          label: 'Dashboards',
          disabled: !selectedProject
        },
        {
          path: '/ffsql-reports',
          icon: BarChart3,
          label: 'FFSQL Reports',
          disabled: !selectedProject
        },
        {
          path: '/dax',
          icon: Code,
          label: 'DAX Conversions',
          disabled: !selectedProject
        },
        {
          path: '/specs',
          icon: FileText,
          label: 'Specs'
        },
      ]
    },
    {
      title: "Help",
      items: [
        { path: '/help', icon: HelpCircle, label: 'Help & Resources' },
      ]
    },
    {
      title: "Settings",
      items: [
        { 
          path: '/environments',
          icon: Settings,
          label: 'Environments',
          customComponent: (
            <EnvironmentsMenuItem isOpen={isOpen} />
          )
        }
      ]
    }
  ];

  return (
    <aside 
      className={cn(
        "fixed inset-y-0 left-0 z-20 flex flex-col",
        "bg-background/95 backdrop-blur-sm shadow-md border-r border-border",
        "transition-all duration-300 ease-apple",
        sidebarWidth,
        !isOpen && "items-center"
      )}
    >
      <div className="flex items-center justify-between h-16 px-4">
        <div className="flex items-center space-x-2">
          <img 
            src={isOpen ? "/application_logo.png" : "/lovable-uploads/67006617-5581-4839-8344-4baf7e4a0557.png"} 
            alt="BI Accelerator Logo" 
            className={cn(
              "transition-all duration-300 ease-apple",
              isOpen ? "h-12" : "h-12 w-12"
            )} 
          />
        </div>
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={toggleSidebar}
          className="hidden md:flex hover:bg-accent/50"
        >
          {isOpen ? <ChevronLeft size={18} /> : <ChevronRight size={18} />}
        </Button>
      </div>
      
      <Separator />
      
      {/* Project context information */}
      {/* {isInProject && selectedProject && isOpen && (
        <div className="px-3 py-2 mb-2">
          <Link 
            to="/projects" 
            className="flex items-center text-sm text-muted-foreground hover:text-foreground mb-1 transition-colors"
          >
            <ArrowLeft className="h-3.5 w-3.5 mr-1" />
            Back to Projects
          </Link>
          <div className="font-medium truncate text-sm">
            Project: {selectedProject.name}
          </div>
          <Separator className="my-2" />
        </div>
      )} */}
      
      {/* Sidebar menu groups */}
      <nav className="flex-1 px-2 py-4 space-y-4 overflow-y-auto">
        {menuGroups.map((group, groupIndex) => (
          <div key={groupIndex} className="space-y-1">
            {isOpen && (
              <h3 className="px-3 text-xs font-medium text-muted-foreground uppercase tracking-wider mb-1">
                {group.title}
              </h3>
            )}
            
            {group.items.map((item) => (
              item.customComponent ? (
                <div
                  key={item.label}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm font-medium rounded-lg",
                    "transition-all duration-200 ease-out group",
                    isActive(item.path) 
                      ? "bg-brand-50 text-brand-600" 
                      : "text-foreground/80 hover:bg-accent/50 hover:text-foreground",
                    !isOpen && "justify-center"
                  )}
                >
                  {item.customComponent}
                </div>
              ) : (
                <Link
                  key={item.label}
                  to={item.path}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm font-medium rounded-lg",
                    "transition-all duration-200 ease-out group",
                    isActive(item.path) 
                      ? "bg-brand-50 text-brand-600" 
                      : "text-foreground/80 hover:bg-accent/50 hover:text-foreground",
                    !isOpen && "justify-center",
                    item.disabled && "opacity-50 cursor-not-allowed pointer-events-none"
                  )}
                >
                  <item.icon 
                    className={cn(
                      "flex-shrink-0 w-5 h-5",
                      !isOpen && "mx-auto"
                    )}
                  />
                  {isOpen && (
                    <span className="ml-3 animate-fade-in">{item.label}</span>
                  )}
                </Link>
              )
            ))}
            
            {isOpen && groupIndex < menuGroups.length - 1 && (
              <Separator className="my-2" />
            )}
          </div>
        ))}
      </nav>
    </aside>
  );
};

// Environment settings menu item
const EnvironmentsMenuItem = ({ isOpen }: { isOpen: boolean }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  
  const isActive = location.pathname === '/environments';
  
  const handleEnvironmentManagerClose = () => {
    setIsDialogOpen(false);
    // Clear selected project when environment changes
    localStorage.removeItem('selectedProject');
    // Always force a full page reload to ensure consistent state across the application
    window.location.href = '/';
  };
  
  return (
    <>
      <div
        onClick={() => setIsDialogOpen(true)}
        className={cn(
          "flex items-center w-full px-3 py-2 text-sm font-medium rounded-lg cursor-pointer",
          "transition-all duration-200 ease-out group",
          isActive 
            ? "bg-brand-50 text-brand-600" 
            : "text-foreground/80 hover:bg-accent/50 hover:text-foreground",
          !isOpen && "justify-center"
        )}
      >
        <Settings 
          className={cn(
            "flex-shrink-0 w-5 h-5",
            !isOpen && "mx-auto"
          )}
        />
        {isOpen && (
          <span className="ml-3 animate-fade-in">Environments</span>
        )}
      </div>
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl">
          <EnvironmentManager onClose={handleEnvironmentManagerClose} />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Sidebar;
