import React from 'react';
import { AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SpecsErrorStateProps {
  title?: string;
  message?: string;
  actionLabel?: string;
  onAction?: () => void;
}

const SpecsErrorState: React.FC<SpecsErrorStateProps> = ({
  title = "Something went wrong",
  message = "Please check your connection and try again.",
  actionLabel = "Try Again",
  onAction
}) => {
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="bg-red-50 text-red-800 px-4 py-3 rounded-md mb-6 border border-red-200">
        <div className="flex items-center gap-2 mb-2">
          <AlertCircle className="h-5 w-5" />
          <p className="font-medium">{title}</p>
        </div>
        <p className="text-sm">{message}</p>
        {actionLabel && onAction && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onAction}
            className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
          >
            {actionLabel}
          </Button>
        )}
      </div>
    </div>
  );
};

export default SpecsErrorState;
