import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Folder, Search, Grid, List, Lock } from 'lucide-react';
import { apiService, Project } from '@/services/api';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useViewMode } from '@/lib/ViewModeContext';

const SpecsPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const { viewMode, setViewMode } = useViewMode();

  const { data: projects, isLoading, error } = useQuery({
    queryKey: ['projects'],
    queryFn: () => apiService.getProjects(),
  });

  // Sort projects: accessible ones first, then non-accessible ones
  const sortedProjects = React.useMemo(() => {
    if (!projects) return [];
    return [...projects].sort((a, b) => {
      if (a.has_access && !b.has_access) return -1;
      if (!a.has_access && b.has_access) return 1;
      return 0;
    });
  }, [projects]);

  const filteredProjects = sortedProjects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleProjectClick = (project: Project) => {
    if (!project.has_access) {
      return; // Don't proceed if no access
    }
    // Store selected project in localStorage
    localStorage.setItem('selectedProject', JSON.stringify(project));

    // Redirect to specs project detail page
    navigate(`/specs/${project.id}`);
  };

  return (
    <div className="container mx-auto px-6 py-6 animate-fade-in">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-sm mb-6">
        <Button
          variant="link"
          className="p-0 h-auto text-orange-500 hover:text-orange-600"
          onClick={() => navigate('/')}
        >
          Home
        </Button>
        <span className="text-muted-foreground">›</span>
        <span className="text-foreground font-medium">Projects</span>
      </div>

      {/* Search and view controls */}
      <div className="flex items-center justify-between mb-8">
        <div className="relative w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects..."
            className="pl-10 border-gray-300 rounded-lg"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'card' | 'list')}>
          <ToggleGroupItem value="card" aria-label="Card View" className="border border-gray-300">
            <Grid className="h-4 w-4" />
          </ToggleGroupItem>
          <ToggleGroupItem value="list" aria-label="List View" className="border border-gray-300">
            <List className="h-4 w-4" />
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      {/* Projects grid/list section */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <Skeleton className="h-6 w-6 mr-3" />
                <Skeleton className="h-6 w-32" />
              </div>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4 mb-4" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h3 className="text-red-700 font-medium mb-2">Error Loading Projects</h3>
          <p className="text-red-600 mb-4">
            There was an error loading the projects. Please check your connection and try again.
          </p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="text-red-600 border-red-200 hover:bg-red-50"
          >
            Retry
          </Button>
        </div>
      ) : filteredProjects?.length === 0 ? (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
            <Folder className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-2 text-gray-900">No Projects Found</h3>
          <p className="text-gray-500">
            {searchTerm
              ? `No projects match "${searchTerm}"`
              : "There are no projects available"
            }
          </p>
        </div>
      ) : viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects?.map((project, index) => (
            <div
              key={project.id}
              className={`bg-white border border-gray-200 rounded-lg p-6 shadow-sm transition-all duration-200 ${
                project.has_access ? 'cursor-pointer hover:shadow-md hover:border-gray-300' : 'opacity-60'
              }`}
              onClick={() => handleProjectClick(project)}
            >
              {/* Project Icon and Name */}
              <div className="flex items-center mb-4">
                <div className="p-2 rounded-lg bg-orange-100 mr-3">
                  <Folder className="h-5 w-5 text-orange-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 truncate">{project.name}</h3>
              </div>

              {/* Project ID */}
              <div className="mb-4">
                <p className="text-sm text-gray-500 mb-1">Project ID:</p>
                <p className="text-sm font-mono text-gray-700 bg-gray-50 rounded px-2 py-1 break-all">
                  {project.id}
                </p>
              </div>

              {/* Access Status */}
              {!project.has_access && (
                <div className="flex items-center text-sm text-gray-500 mb-4">
                  <Lock className="h-4 w-4 mr-1" />
                  No access
                </div>
              )}

              {/* Action Button */}
              <Button
                className={`w-full ${
                  project.has_access
                    ? 'bg-orange-500 hover:bg-orange-600 text-white'
                    : 'bg-orange-200 text-orange-400 cursor-not-allowed'
                }`}
                disabled={!project.has_access}
                onClick={(e) => {
                  e.stopPropagation();
                  handleProjectClick(project);
                }}
              >
                {project.has_access ? 'Browse Project' : 'No Access'}
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex flex-col gap-3">
          {filteredProjects?.map((project, index) => (
            <div
              key={project.id}
              className={`flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm transition-all duration-200 ${
                project.has_access ? 'cursor-pointer hover:shadow-md hover:border-gray-300' : 'opacity-60'
              }`}
              onClick={() => handleProjectClick(project)}
            >
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-orange-100 mr-3">
                  <Folder className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{project.name}</h3>
                  <p className="text-sm text-gray-500">
                    Project ID: <span className="font-mono text-xs bg-gray-50 rounded px-1 py-0.5">{project.id}</span>
                  </p>
                  {!project.has_access && (
                    <div className="flex items-center text-sm text-gray-500 mt-1">
                      <Lock className="h-4 w-4 mr-1" />
                      No access
                    </div>
                  )}
                </div>
              </div>
              <Button
                size="sm"
                disabled={!project.has_access}
                className={`${
                  project.has_access
                    ? 'bg-orange-500 hover:bg-orange-600 text-white'
                    : 'bg-orange-200 text-orange-400 cursor-not-allowed'
                } w-32`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleProjectClick(project);
                }}
              >
                {project.has_access ? 'Browse Project' : 'No Access'}
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SpecsPage;
