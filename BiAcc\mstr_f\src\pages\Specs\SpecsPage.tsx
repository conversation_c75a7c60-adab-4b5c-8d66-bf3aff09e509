import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Folder, Search, Info, Grid, List, ChevronRight, Lock } from 'lucide-react';
import { apiService, Project } from '@/services/api';
import { Input } from '@/components/ui/input';
import Card, { CardHeader, CardTitle, CardContent, CardFooter } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { Skeleton } from '@/components/ui/skeleton';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useViewMode } from '@/lib/ViewModeContext';

const SpecsPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const { viewMode, setViewMode } = useViewMode();
  
  const { data: projects, isLoading, error } = useQuery({
    queryKey: ['projects'],
    queryFn: () => apiService.getProjects(),
  });
  
  // Sort projects: accessible ones first, then non-accessible ones
  const sortedProjects = React.useMemo(() => {
    if (!projects) return [];
    return [...projects].sort((a, b) => {
      if (a.has_access && !b.has_access) return -1;
      if (!a.has_access && b.has_access) return 1;
      return 0;
    });
  }, [projects]);
  
  const filteredProjects = sortedProjects.filter(project => 
    project.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleProjectClick = (project: Project) => {
    if (!project.has_access) {
      return; // Don't proceed if no access
    }
    // Store selected project in localStorage
    localStorage.setItem('selectedProject', JSON.stringify(project));
    // Redirect to spec detail page
    navigate(`/specs/${project.id}`);
  };
  
  return (
    <div className="max-w-7xl mx-auto px-4 py-8 animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>Home</Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">Specs</span>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">Projects</span>
      </div>
      {/* Search and view controls */}
      <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
        <div className="relative max-w-md w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search specs..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'card' | 'list')}>
          <ToggleGroupItem value="card" aria-label="Card View">
            <Grid className="h-4 w-4" />
          </ToggleGroupItem>
          <ToggleGroupItem value="list" aria-label="List View">
            <List className="h-4 w-4" />
          </ToggleGroupItem>
        </ToggleGroup>
      </div>
      {/* Specs grid/list section */}
      {isLoading ? (
        <div className={viewMode === 'card' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "flex flex-col gap-4"}>
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-8 w-3/4 mb-2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-10 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : error ? (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-700 flex items-center">
              <Info className="mr-2 h-5 w-5" />
              Error Loading Specs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">
              There was an error loading the specs. Please check your connection and try again.
            </p>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={() => window.location.reload()}
              variant="outline"
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              Retry
            </Button>
          </CardFooter>
        </Card>
      ) : filteredProjects?.length === 0 ? (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <Folder className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Specs Found</h3>
          <p className="text-muted-foreground">
            {searchTerm 
              ? `No specs match "${searchTerm}"`
              : "There are no specs available"
            }
          </p>
        </div>
      ) : viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects?.map((project, index) => (
            <Card 
              key={project.id} 
              className={`group transition-all duration-300 animate-slide-up delay-${index * 50} ${project.has_access ? 'cursor-pointer hover:shadow-md hover:-translate-y-1' : 'opacity-75'}`}
            >
              <div onClick={() => handleProjectClick(project)}>
                <CardHeader>
                  <div className="flex items-center">
                    <div className="p-2 rounded-lg bg-brand-100 mr-3">
                      <Folder className="h-5 w-5 text-brand-600" />
                    </div>
                    <CardTitle className="truncate">{project.name}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-2">
                    Spec ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{project.id}</span>
                  </p>
                  {!project.has_access && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Lock className="h-4 w-4 mr-1" />
                      No access
                    </div>
                  )}
                </CardContent>
                <CardFooter>
                  <Button 
                    variant="default" 
                    fullWidth
                    disabled={!project.has_access}
                    className={`group-hover:bg-brand-600 transition-colors ${!project.has_access ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleProjectClick(project);
                    }}
                  >
                    {project.has_access ? 'Browse Project' : 'No Access'}
                  </Button>
                </CardFooter>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <div className="flex flex-col gap-3">
          {filteredProjects?.map((project, index) => (
            <div 
              key={project.id} 
              className={`group flex items-center justify-between p-4 rounded-lg border border-border bg-card transition-all duration-300 animate-slide-up delay-${index * 50} ${project.has_access ? 'cursor-pointer hover:shadow-md hover:bg-accent/5' : 'opacity-75'}`}
              onClick={() => handleProjectClick(project)}
            >
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-brand-100 mr-3">
                  <Folder className="h-5 w-5 text-brand-600" />
                </div>
                <div>
                  <h3 className="font-semibold">{project.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    Spec ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{project.id}</span>
                  </p>
                  {!project.has_access && (
                    <div className="flex items-center text-sm text-muted-foreground mt-1">
                      <Lock className="h-4 w-4 mr-1" />
                      No access
                    </div>
                  )}
                </div>
              </div>
              <Button 
                variant="default" 
                disabled={!project.has_access}
                className={`group-hover:bg-brand-600 transition-colors ${!project.has_access ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleProjectClick(project);
                }}
              >
                {project.has_access ? 'Browse Project' : 'No Access'}
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SpecsPage; 