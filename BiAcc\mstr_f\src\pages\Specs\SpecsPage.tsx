import React from 'react';
import { useNavigate } from 'react-router-dom';

const SpecsPage = () => {
  const navigate = useNavigate();

  return (
    <div className="container mx-auto px-6 py-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
        <h1 className="text-2xl font-bold text-green-800 mb-2">✅ Specs Page is Working!</h1>
        <p className="text-green-700">This is the custom Specs Projects page. The routing is working correctly.</p>
      </div>

      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-sm mb-6">
        <button
          className="text-orange-500 hover:text-orange-600 underline"
          onClick={() => navigate('/')}
        >
          Home
        </button>
        <span className="text-gray-500">›</span>
        <span className="text-gray-900 font-medium">Projects</span>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Projects</h2>
        <p className="text-gray-600 mb-4">This will show the projects list once we fix any API issues.</p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Mock project cards */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <div className="flex items-center mb-4">
              <div className="p-2 rounded-lg bg-orange-100 mr-3">
                <svg className="h-5 w-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Test Project 1</h3>
            </div>
            <div className="mb-4">
              <p className="text-sm text-gray-500 mb-1">Project ID:</p>
              <p className="text-sm font-mono text-gray-700 bg-gray-50 rounded px-2 py-1">test-project-1</p>
            </div>
            <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded">
              Browse Project
            </button>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm opacity-60">
            <div className="flex items-center mb-4">
              <div className="p-2 rounded-lg bg-orange-100 mr-3">
                <svg className="h-5 w-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Test Project 2</h3>
            </div>
            <div className="mb-4">
              <p className="text-sm text-gray-500 mb-1">Project ID:</p>
              <p className="text-sm font-mono text-gray-700 bg-gray-50 rounded px-2 py-1">test-project-2</p>
              <div className="flex items-center text-sm text-gray-500 mt-2">
                <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                No access
              </div>
            </div>
            <button className="w-full bg-orange-200 text-orange-400 cursor-not-allowed py-2 px-4 rounded">
              No Access
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpecsPage;
