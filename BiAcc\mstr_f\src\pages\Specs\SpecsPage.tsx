import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ChevronRight, Search, Grid, List, Lock, FolderTree } from 'lucide-react';
import { apiService, Project } from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useViewMode } from '@/lib/ViewModeContext';

const SpecsPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const { viewMode, setViewMode } = useViewMode();

  const { data: projects, isLoading, error } = useQuery({
    queryKey: ['projects'],
    queryFn: apiService.getProjects,
  });

  const filteredProjects = searchTerm 
    ? projects?.filter(project => 
        project.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : projects;

  const handleProjectClick = (project: Project) => {
    if (!project.has_access) {
      return; // Don't navigate if no access
    }
    
    // Store the selected project in localStorage
    localStorage.setItem('selectedProject', JSON.stringify(project));
    // Navigate to the project detail page
    navigate(`/specs/${project.id}`);
  };

  if (error) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="bg-red-50 text-red-800 px-4 py-3 rounded-md mb-6 border border-red-200">
          <p className="font-medium">Failed to load projects</p>
          <p className="text-sm mt-1">Please check your connection and try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container px-4 mx-auto animate-fade-in">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" asChild>
          <Link to="/">Home</Link>
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground">Specs</span>
      </div>
      
      {/* Header */}
      <div className="flex flex-col mb-8">
        <h1 className="text-2xl font-bold mb-1">Specs</h1>
        <p className="text-muted-foreground mb-6">
          Browse and analyze data resources across projects
        </p>
        
        {/* Search and View Toggle */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="relative max-w-md w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search projects..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              disabled={isLoading}
            />
          </div>
          <div className="flex items-center gap-4">
            <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'card' | 'list')}>
              <ToggleGroupItem value="card" aria-label="Card view">
                <Grid className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="list" aria-label="List view">
                <List className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className={viewMode === 'card' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
          {[...Array(6)].map((_, i) => (
            <div key={i}>
              <Card>
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-8 w-24" />
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      ) : filteredProjects && filteredProjects.length > 0 ? (
        /* Project List */
        <div className={viewMode === 'card' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" : "space-y-4"}>
          {filteredProjects.map((project, index) => (
            <Card 
              key={project.id} 
              className={`group transition-all duration-300 animate-slide-up delay-${index * 50} ${
                project.has_access 
                  ? 'cursor-pointer hover:shadow-md hover:-translate-y-1' 
                  : 'opacity-75 cursor-not-allowed'
              }`}
            >
              <div onClick={() => handleProjectClick(project)}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <span className="truncate">{project.name}</span>
                      {!project.has_access && (
                        <Lock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      )}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <Badge 
                      variant={project.has_access ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {project.has_access ? "Accessible" : "Restricted"}
                    </Badge>
                    {project.has_access && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        Browse →
                      </Button>
                    )}
                  </div>
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        /* Empty State */
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <FolderTree className="h-8 w-8 text-muted-foreground" />
          </div>
          <h2 className="text-xl font-semibold mb-2">No Projects Found</h2>
          <p className="text-muted-foreground max-w-md mx-auto mb-6">
            {searchTerm 
              ? `No projects matching "${searchTerm}" were found. Try a different search term.` 
              : 'There are no projects available. Please check your access permissions.'}
          </p>
        </div>
      )}
    </div>
  );
};

export default SpecsPage;
