import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ChevronLeft, BarChart3, List, Database, Code, ChevronRight, Search, Play } from 'lucide-react';
import { 
  apiService, 
  Project, 
  CubeDetail as ICubeDetail,
  SQLAnalysisVisualResponse,
  SQLAnalysisTabularResponse,
  SQLAnalysisRawResponse,
  SQLAnalysisTable
} from '@/services/api';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import Card, { CardHeader, CardTitle, CardContent } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader } from '@/components/shared/Loader';
import SQLVisualization from '@/components/SQLVisualization';
import ExpressionModal from '@/components/ExpressionModal';

const BASE_URL = 'http://localhost:8001/api';

const SpecCubeDetail = () => {
  const { id, cubeId } = useParams<{ id: string; cubeId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'info');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  
  // New state for imported SQL
  const [importedSql, setImportedSql] = useState<string | null>(null);
  
  // New state for analysis format
  const [analysisFormat, setAnalysisFormat] = useState<'visual' | 'tabular'>('visual');
  const [rawAnalysisData, setRawAnalysisData] = useState<SQLAnalysisRawResponse | null>(null);
  
  const [selectedItem, setSelectedItem] = useState<{ id: string; name: string; type: 'attribute' | 'metric' } | null>(null);
  const [isExpressionModalOpen, setIsExpressionModalOpen] = useState(false);
  
  // Load selected project from localStorage and validate it matches the URL param
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      const project = JSON.parse(storedProject);
      // Ensure the project from storage matches the ID from the URL
      if (project.id === id) {
        setSelectedProject(project);
      } else {
        // If not, it's possible the user navigated directly
        // or the stored project is stale. For now, we'll clear it.
        localStorage.removeItem('selectedProject');
        navigate('/specs');
      }
    } else {
      navigate('/specs');
    }
  }, [id, navigate]);
  
  // Check if there's any SQL in session storage to analyze
  useEffect(() => {
    if (activeTab === 'analyze') {
      const storedSql = sessionStorage.getItem('analyze_sql');
      if (storedSql) {
        setImportedSql(storedSql);
        // Only set shouldAnalyze to true if we don't already have analysis data
        if (!rawAnalysisData) {
          setShouldAnalyze(true);
        }
        sessionStorage.removeItem('analyze_sql'); // Clear after use
      }
    }
  }, [activeTab, rawAnalysisData]);
  
  // Update the URL when tab changes
  useEffect(() => {
    const currentParams = new URLSearchParams(location.search);
    if (activeTab !== 'info') {
      currentParams.set('tab', activeTab);
    } else {
      currentParams.delete('tab');
    }
    
    const newSearch = currentParams.toString();
    const newPath = location.pathname + (newSearch ? `?${newSearch}` : '');
    
    // Only update if different to avoid loops
    if (location.search !== (newSearch ? `?${newSearch}` : '')) {
      navigate(newPath, { replace: true });
    }
  }, [activeTab, location, navigate]);
  
  const { data: cube, isLoading, error } = useQuery({
    queryKey: ['cube', cubeId, selectedProject?.id],
    queryFn: () => selectedProject && cubeId ? apiService.getCube(cubeId, selectedProject.id) : Promise.resolve(null),
    enabled: !!selectedProject && !!cubeId,
  });
  
  const { data: cubeSql, isLoading: isSqlLoading } = useQuery({
    queryKey: ['cube-sql', cubeId, selectedProject?.id],
    queryFn: () => selectedProject && cubeId ? apiService.getCubeSql(cubeId, selectedProject.id) : Promise.resolve(null),
    enabled: !!selectedProject && !!cubeId && (activeTab === 'sql' || activeTab === 'analyze'),
  });
  
  // New state and query for analyze tab
  const [shouldAnalyze, setShouldAnalyze] = useState(false);
  const [sqlToAnalyze, setSqlToAnalyze] = useState<string>('');
  
  // Set SQL to analyze when cubeSql is loaded or importedSql is available
  useEffect(() => {
    if (importedSql) {
      setSqlToAnalyze(importedSql);
    } else if (cubeSql?.sql) {
      setSqlToAnalyze(cubeSql.sql);
    }
  }, [cubeSql, importedSql]);
  
  const { data: rawData, isLoading: isAnalysisLoading } = useQuery({
    queryKey: ['sql-analysis', sqlToAnalyze, selectedProject?.id],
    queryFn: () => {
      if (!selectedProject || !sqlToAnalyze) return Promise.resolve(null);
      return apiService.analyzeSql(sqlToAnalyze, selectedProject.id);
    },
    enabled: !!selectedProject && !!sqlToAnalyze && shouldAnalyze && activeTab === 'analyze',
    onSuccess: (data) => {
      if (data) {
        setRawAnalysisData(data);
        setShouldAnalyze(false); // Reset the flag after successful analysis
      }
    },
  });
  
  // Process analysis data based on format
  const analysisData = useMemo(() => {
    if (!rawAnalysisData) return null;
    
    if (analysisFormat === 'tabular') {
      // Convert to tabular format
      const tabularData: SQLAnalysisTabularResponse = {
        tables: rawAnalysisData.main_tables.map(table => ({
          name: table.name,
          columns: table.columns || [],
          relationships: rawAnalysisData.relationships.filter(rel => 
            rel.from_table === table.name || rel.to_table === table.name
          )
        }))
      };
      
      return tabularData;
    } else {
      // Convert to visual format
      const visualData: SQLAnalysisVisualResponse = {
        nodes: rawAnalysisData.main_tables,
        links: rawAnalysisData.relationships,
        model_structure: rawAnalysisData.model_structure,
      };
      
      return visualData;
    }
  }, [rawData, rawAnalysisData, analysisFormat]);
  
  // Function to trigger analysis
  const handleAnalyze = () => {
    if (sqlToAnalyze) {
      setShouldAnalyze(true);
      // Clear any existing analysis data to ensure fresh analysis
      setRawAnalysisData(null);
    }
  };
  
  // Handle attribute or metric click
  const handleItemClick = (id: string, name: string, type: 'attribute' | 'metric') => {
    setSelectedItem({ id, name, type });
    setIsExpressionModalOpen(true);
  };
  
  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>No project selected</AlertTitle>
          <AlertDescription>
            Please select a project from the specs page first.
            <Button
              variant="link"
              className="p-0 ml-2"
              onClick={() => navigate('/specs')}
            >
              Go to Specs
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>Error Loading Cube</AlertTitle>
          <AlertDescription>
            There was an error loading the cube details. Please try again.
            <Button
              variant="link"
              className="p-0 ml-2"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />        
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/specs')}>
          Specs
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate(`/specs/${selectedProject.id}`)}>
          {selectedProject.name}
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate(`/specs/${selectedProject.id}/cubes`)}>
          Cubes
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          {isLoading ? 'Loading...' : cube?.name || 'Cube Details'}
        </span>
      </div>
      
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            className="mr-4"
            onClick={() => navigate(`/specs/${selectedProject.id}/cubes`)}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-blue-100 mr-3">
              <Database className="h-6 w-6 text-blue-600" />
            </div>
            {isLoading ? (
              <Skeleton className="h-8 w-40" />
            ) : (
              <h1 className="text-2xl font-bold">{cube?.name || 'Cube Not Found'}</h1>
            )}
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader />
        </div>
      ) : error ? (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-700">Error Loading Cube</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">
              There was an error loading the cube details. Please check your connection and try again.
            </p>
          </CardContent>
        </Card>
      ) : !cube ? (
        <Card>
          <CardHeader>
            <CardTitle>Cube Not Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              The requested cube could not be found.
            </p>
          </CardContent>
        </Card>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="info" className="flex items-center gap-2">
              <List className="h-4 w-4" />
              Info
            </TabsTrigger>
            <TabsTrigger value="attributes" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Attributes
            </TabsTrigger>
            <TabsTrigger value="sql" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              SQL
            </TabsTrigger>
            <TabsTrigger value="analyze" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Analyze
            </TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Name</label>
                    <p className="text-sm font-mono bg-muted rounded px-2 py-1 mt-1">{cube.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Type</label>
                    <p className="text-sm font-mono bg-muted rounded px-2 py-1 mt-1">{cube.type}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">ID</label>
                    <p className="text-sm font-mono bg-muted rounded px-2 py-1 mt-1">{cube.id}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Attributes</label>
                    <p className="text-2xl font-bold text-blue-600">{cube.attributes?.length || 0}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Metrics</label>
                    <p className="text-2xl font-bold text-green-600">{cube.metrics?.length || 0}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="attributes" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                    Attributes ({cube.attributes?.length || 0})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    <div className="space-y-2">
                      {cube.attributes?.map((attr) => (
                        <div
                          key={attr.id}
                          className="p-3 rounded-lg border border-border hover:bg-accent/50 cursor-pointer transition-colors"
                          onClick={() => handleItemClick(attr.id, attr.name, 'attribute')}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{attr.name}</span>
                            <span className="text-xs text-muted-foreground bg-muted rounded px-2 py-1">
                              {attr.type}
                            </span>
                          </div>
                          {attr.description && (
                            <p className="text-sm text-muted-foreground mt-1">{attr.description}</p>
                          )}
                        </div>
                      )) || (
                        <p className="text-muted-foreground text-center py-8">No attributes available</p>
                      )}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-green-600" />
                    Metrics ({cube.metrics?.length || 0})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-96">
                    <div className="space-y-2">
                      {cube.metrics?.map((metric) => (
                        <div
                          key={metric.id}
                          className="p-3 rounded-lg border border-border hover:bg-accent/50 cursor-pointer transition-colors"
                          onClick={() => handleItemClick(metric.id, metric.name, 'metric')}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">{metric.name}</span>
                            <span className="text-xs text-muted-foreground bg-muted rounded px-2 py-1">
                              {metric.type}
                            </span>
                          </div>
                          {metric.description && (
                            <p className="text-sm text-muted-foreground mt-1">{metric.description}</p>
                          )}
                        </div>
                      )) || (
                        <p className="text-muted-foreground text-center py-8">No metrics available</p>
                      )}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="sql" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  SQL Definition
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isSqlLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader />
                  </div>
                ) : cubeSql?.sql ? (
                  <div className="relative">
                    <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                      <code>{cubeSql.sql}</code>
                    </pre>
                  </div>
                ) : (
                  <p className="text-muted-foreground text-center py-8">No SQL definition available</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analyze" className="mt-6">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Search className="h-5 w-5" />
                    SQL Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4 mb-4">
                    <Button
                      onClick={handleAnalyze}
                      disabled={!sqlToAnalyze || isAnalysisLoading}
                      className="flex items-center gap-2"
                    >
                      <Play className="h-4 w-4" />
                      {isAnalysisLoading ? 'Analyzing...' : 'Analyze SQL'}
                    </Button>

                    <div className="flex items-center gap-2">
                      <label className="text-sm font-medium">Format:</label>
                      <select
                        value={analysisFormat}
                        onChange={(e) => setAnalysisFormat(e.target.value as 'visual' | 'tabular')}
                        className="px-3 py-1 border border-border rounded-md text-sm"
                      >
                        <option value="visual">Visual</option>
                        <option value="tabular">Tabular</option>
                      </select>
                    </div>
                  </div>

                  {isAnalysisLoading ? (
                    <div className="flex justify-center items-center py-12">
                      <Loader />
                    </div>
                  ) : analysisData ? (
                    <SQLVisualization
                      data={analysisData}
                      format={analysisFormat}
                    />
                  ) : (
                    <p className="text-muted-foreground text-center py-8">
                      Click "Analyze SQL" to see the analysis results
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      )}

      <ExpressionModal
        isOpen={isExpressionModalOpen}
        onClose={() => setIsExpressionModalOpen(false)}
        item={selectedItem}
        projectId={selectedProject?.id}
      />
    </div>
  );
};

export default SpecCubeDetail;
