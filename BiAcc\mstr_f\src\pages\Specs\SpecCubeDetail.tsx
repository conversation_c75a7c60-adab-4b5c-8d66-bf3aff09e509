import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ChevronRight, ArrowLeft, Database, Code, Play, Loader2 } from 'lucide-react';
import { apiService, Project } from '@/services/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const SpecCubeDetail = () => {
  const { id, cubeId } = useParams<{ id: string; cubeId: string }>();
  const navigate = useNavigate();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [sqlAnalysisLoading, setSqlAnalysisLoading] = useState(false);

  useEffect(() => {
    // Try to get project from localStorage
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      const project = JSON.parse(storedProject);
      if (project.id === id) {
        setSelectedProject(project);
        return;
      }
    }
    
    // If no matching project in localStorage, redirect to specs page
    navigate('/specs');
  }, [id, navigate]);

  const { data: cubeDetail, isLoading, error } = useQuery({
    queryKey: ['cube-detail', cubeId, id],
    queryFn: () => selectedProject && cubeId ? apiService.getCubeDetail(cubeId, selectedProject.id) : Promise.resolve(null),
    enabled: !!selectedProject && !!cubeId,
  });

  const handleSqlAnalysis = async () => {
    if (!cubeDetail || !selectedProject) return;
    
    setSqlAnalysisLoading(true);
    try {
      // Navigate to the existing cube detail page which has SQL analysis
      navigate(`/cubes/${cubeId}?project_id=${selectedProject.id}`);
    } catch (error) {
      console.error('Failed to navigate to SQL analysis:', error);
    } finally {
      setSqlAnalysisLoading(false);
    }
  };

  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <p className="text-muted-foreground mb-4">Loading project details...</p>
            <Button onClick={() => navigate('/specs')}>
              Back to Specs
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <div className="bg-red-50 text-red-800 px-4 py-3 rounded-md mb-6 border border-red-200">
          <p className="font-medium">Failed to load cube details</p>
          <p className="text-sm mt-1">Please check your connection and try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container px-4 mx-auto animate-fade-in">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 text-sm mb-6">
        <Button variant="link" className="p-0 h-auto text-orange-500 hover:text-orange-600" asChild>
          <Link to="/">Home</Link>
        </Button>
        <span className="text-muted-foreground">›</span>
        <Button variant="link" className="p-0 h-auto text-orange-500 hover:text-orange-600" asChild>
          <Link to="/specs">Projects</Link>
        </Button>
        <span className="text-muted-foreground">›</span>
        <Button variant="link" className="p-0 h-auto text-orange-500 hover:text-orange-600" asChild>
          <Link to={`/specs/${id}`}>{selectedProject.name}</Link>
        </Button>
        <span className="text-muted-foreground">›</span>
        <Button variant="link" className="p-0 h-auto text-orange-500 hover:text-orange-600" asChild>
          <Link to={`/specs/${id}/cubes`}>Cubes</Link>
        </Button>
        <span className="text-muted-foreground">›</span>
        <span className="font-medium text-foreground truncate max-w-xs">
          {cubeDetail?.name || 'Cube Details'}
        </span>
      </div>

      {/* Back Button */}
      <div className="mb-4">
        <Button variant="ghost" onClick={() => navigate(`/specs/${id}/cubes`)} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Cubes
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-6">
          <div>
            <Skeleton className="h-8 w-1/3 mb-2" />
            <Skeleton className="h-4 w-2/3" />
          </div>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-1/4" />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </CardContent>
          </Card>
        </div>
      ) : cubeDetail ? (
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col">
            <div className="flex items-center gap-3 mb-2">
              <Database className="h-6 w-6 text-blue-600" />
              <h1 className="text-2xl font-bold">{cubeDetail.name}</h1>
            </div>
            <p className="text-muted-foreground mb-4">
              Analyze cube structure and generate SQL insights
            </p>
            <div className="flex gap-2">
              <Badge variant="secondary">{cubeDetail.type}</Badge>
              <Button 
                onClick={handleSqlAnalysis}
                disabled={sqlAnalysisLoading}
                className="gap-2"
              >
                {sqlAnalysisLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Code className="h-4 w-4" />
                )}
                SQL Analysis
              </Button>
            </div>
          </div>

          {/* Cube Details */}
          <Tabs defaultValue="overview" className="w-full">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="attributes">Attributes</TabsTrigger>
              <TabsTrigger value="metrics">Metrics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Cube Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Cube ID</p>
                      <p className="text-sm">{cubeDetail.id}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Type</p>
                      <p className="text-sm">{cubeDetail.type}</p>
                    </div>
                    {cubeDetail.properties?.description && (
                      <div className="md:col-span-2">
                        <p className="text-sm font-medium text-muted-foreground">Description</p>
                        <p className="text-sm">{cubeDetail.properties.description}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {cubeDetail.properties && (
                <Card>
                  <CardHeader>
                    <CardTitle>Properties</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {cubeDetail.properties.date_created && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Created</p>
                          <p className="text-sm">{new Date(cubeDetail.properties.date_created).toLocaleDateString()}</p>
                        </div>
                      )}
                      {cubeDetail.properties.date_modified && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Modified</p>
                          <p className="text-sm">{new Date(cubeDetail.properties.date_modified).toLocaleDateString()}</p>
                        </div>
                      )}
                      {cubeDetail.properties.owner && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Owner</p>
                          <p className="text-sm">{cubeDetail.properties.owner}</p>
                        </div>
                      )}
                      {cubeDetail.properties.version && (
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Version</p>
                          <p className="text-sm">{cubeDetail.properties.version}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="attributes" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Attributes ({cubeDetail.attributes?.length || 0})</CardTitle>
                </CardHeader>
                <CardContent>
                  {cubeDetail.attributes && cubeDetail.attributes.length > 0 ? (
                    <div className="space-y-2">
                      {cubeDetail.attributes.map((attribute) => (
                        <div key={attribute.id} className="flex items-center justify-between p-2 border rounded">
                          <span className="font-medium">{attribute.name}</span>
                          <Badge variant="outline" className="text-xs">{attribute.id}</Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No attributes available</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="metrics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Metrics ({cubeDetail.metrics?.length || 0})</CardTitle>
                </CardHeader>
                <CardContent>
                  {cubeDetail.metrics && cubeDetail.metrics.length > 0 ? (
                    <div className="space-y-2">
                      {cubeDetail.metrics.map((metric) => (
                        <div key={metric.id} className="flex items-center justify-between p-2 border rounded">
                          <span className="font-medium">{metric.name}</span>
                          <Badge variant="outline" className="text-xs">{metric.id}</Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No metrics available</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <Database className="h-8 w-8 text-muted-foreground" />
          </div>
          <h2 className="text-xl font-semibold mb-2">Cube Not Found</h2>
          <p className="text-muted-foreground max-w-md mx-auto mb-6">
            The requested cube could not be found or you don't have access to it.
          </p>
          <Button onClick={() => navigate(`/specs/${id}/cubes`)}>
            Back to Cubes
          </Button>
        </div>
      )}
    </div>
  );
};

export default SpecCubeDetail;
