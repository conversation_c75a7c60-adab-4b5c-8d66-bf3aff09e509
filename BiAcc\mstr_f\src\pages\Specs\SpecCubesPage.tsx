import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Database, Search, Info, ChevronRight, Box, Grid, List } from 'lucide-react';
import { apiService, Cube, Project } from '@/services/api';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import Card, { CardHeader, CardTitle, CardContent, CardFooter } from '@/components/shared/Card';
import Button from '@/components/shared/Button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader } from '@/components/shared/Loader';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { useViewMode } from '@/lib/ViewModeContext';

const SpecCubesPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const { viewMode, setViewMode } = useViewMode();
  
  // Load selected project from localStorage and validate it matches the URL param
  useEffect(() => {
    const storedProject = localStorage.getItem('selectedProject');
    if (storedProject) {
      const project = JSON.parse(storedProject);
      // Ensure the project from storage matches the ID from the URL
      if (project.id === id) {
        setSelectedProject(project);
      } else {
        // If not, it's possible the user navigated directly
        // or the stored project is stale. For now, we'll clear it.
        localStorage.removeItem('selectedProject');
        navigate('/specs');
      }
    } else {
      navigate('/specs');
    }
  }, [id, navigate]);
  
  const { data: cubes, isLoading, error } = useQuery({
    queryKey: ['cubes', selectedProject?.id],
    queryFn: () => selectedProject 
      ? apiService.getCubes(selectedProject.id)
      : Promise.resolve([]),
    enabled: !!selectedProject,
  });
  
  const filteredCubes = cubes?.filter(cube => 
    cube.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleCubeClick = (cube: Cube) => {
    navigate(`/specs/${selectedProject?.id}/cubes/${cube.id}`);
  };
  
  if (!selectedProject) {
    return (
      <div className="container px-4 mx-auto animate-fade-in">
        <Alert>
          <AlertTitle>No project selected</AlertTitle>
          <AlertDescription>
            Please select a project from the specs page first.
            <Button
              variant="link"
              className="p-0 ml-2"
              onClick={() => navigate('/specs')}
            >
              Go to Specs
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return (
    <div className="container px-4 mx-auto animate-fade-in">
      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/')}>
          Home
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate('/specs')}>
          Specs
        </Button>
        <ChevronRight className="h-4 w-4" />
        <Button variant="link" className="p-0 h-auto" onClick={() => navigate(`/specs/${selectedProject.id}`)}>
          {selectedProject.name}
        </Button>
        <ChevronRight className="h-4 w-4" />
        <span className="font-medium text-foreground truncate max-w-xs">
          Cubes
        </span>
      </div>
      
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="relative max-w-md w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search cubes..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-4">
          <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'card' | 'list')}>
            <ToggleGroupItem value="card" aria-label="Card view">
              <Grid className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="list" aria-label="List view">
              <List className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>
      
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader />
        </div>
      ) : error ? (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-700 flex items-center">
              <Info className="mr-2 h-5 w-5" />
              Error Loading Cubes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">
              There was an error loading the cubes. Please check your connection and try again.
            </p>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={() => window.location.reload()}
              variant="outline"
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              Retry
            </Button>
          </CardFooter>
        </Card>
      ) : filteredCubes?.length === 0 ? (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <Database className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Cubes Found</h3>
          <p className="text-muted-foreground">
            {searchTerm 
              ? `No cubes match "${searchTerm}"`
              : "There are no cubes available for this project"
            }
          </p>
        </div>
      ) : viewMode === 'card' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCubes?.map((cube, index) => (
            <Card 
              key={cube.id} 
              hoverable 
              className={`group transition-all duration-300 animate-slide-up delay-${index * 50}`}
              onClick={() => handleCubeClick(cube)}
            >
              <CardHeader>
                <div className="flex items-center">
                  <div className="p-2 rounded-lg bg-blue-100 mr-3">
                    <Database className="h-5 w-5 text-blue-600" />
                  </div>
                  <CardTitle className="truncate">{cube.name}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-2">
                  Type: <span className="font-medium">{cube.type}</span>
                </p>
                <p className="text-sm text-muted-foreground mb-2">
                  ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{cube.id}</span>
                </p>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="default" 
                  fullWidth
                  className="group-hover:bg-brand-600 transition-colors"
                  icon={<Box className="h-4 w-4" />}
                  iconPosition="right"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCubeClick(cube);
                  }}
                >
                  View Cube
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="flex flex-col gap-3">
          {filteredCubes?.map((cube, index) => (
            <div 
              key={cube.id} 
              className={`group flex items-center justify-between p-4 rounded-lg border border-border bg-card transition-all duration-300 animate-slide-up delay-${index * 50} cursor-pointer hover:shadow-md hover:bg-accent/5`}
              onClick={() => handleCubeClick(cube)}
            >
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-blue-100 mr-3">
                  <Database className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold">{cube.name}</h3>
                  <div className="flex gap-4">
                    <p className="text-sm text-muted-foreground">
                      Type: <span className="font-medium">{cube.type}</span>
                    </p>
                    <p className="text-sm text-muted-foreground">
                      ID: <span className="font-mono text-xs bg-muted rounded px-1 py-0.5">{cube.id}</span>
                    </p>
                  </div>
                </div>
              </div>
              <Button 
                variant="default" 
                size="sm"
                className="group-hover:bg-brand-600 transition-colors"
                icon={<Box className="h-4 w-4" />}
                iconPosition="right"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCubeClick(cube);
                }}
              >
                View Cube
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SpecCubesPage;
